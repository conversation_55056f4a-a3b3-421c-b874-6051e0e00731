#!/usr/bin/env python
"""
Quick script to test URL routing for the new product-type brands functionality
"""
import os
import sys
import django

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pc_hardware.settings.dev')
django.setup()

from django.urls import reverse
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from apps.staff.products.models import ProductTypeProxy, BrandProxy

User = get_user_model()

def test_url_routing():
    """Test if the URL routing works correctly"""
    
    print("🔍 Testing URL routing for product-type brands functionality...")
    
    # Test URL reverse
    try:
        # This should work if the URL routing is correct
        url = reverse('staff_products:product-types-brands', kwargs={'pk': 1})
        print(f"✅ URL reverse successful: {url}")
    except Exception as e:
        print(f"❌ URL reverse failed: {e}")
        print("   This might indicate a routing issue")
    
    # Test if we can create the URLs manually
    expected_urls = [
        '/api/staff/products/product-types/1/brands/',
        '/api/staff/products/product-types/1/associate_brands/',
        '/api/staff/products/product-types/1/remove_brands/',
    ]
    
    print(f"\n📋 Expected URLs:")
    for url in expected_urls:
        print(f"   {url}")
    
    # Test with APIClient
    client = APIClient()
    
    # Create a test user
    try:
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_staff=True
        )
        client.force_authenticate(user=user)
        print(f"\n👤 Created test user: {user.email}")
    except Exception as e:
        print(f"❌ Failed to create test user: {e}")
        return
    
    # Test the URLs
    for url in expected_urls:
        try:
            if 'associate_brands' in url or 'remove_brands' in url:
                # POST endpoints
                response = client.post(url, {
                    'product_type_id': 1,
                    'brand_ids': [1]
                })
            else:
                # GET endpoint
                response = client.get(url)
            
            print(f"📡 {url}: Status {response.status_code}")
            
            if response.status_code == 404:
                print(f"   ❌ 404 - URL not found")
            elif response.status_code == 403:
                print(f"   🔒 403 - Permission denied")
            elif response.status_code in [400, 500]:
                print(f"   ⚠️  {response.status_code} - Server error (but URL exists)")
            else:
                print(f"   ✅ URL exists and accessible")
                
        except Exception as e:
            print(f"   ❌ Error testing {url}: {e}")
    
    # Clean up
    try:
        user.delete()
        print(f"\n🧹 Cleaned up test user")
    except:
        pass

if __name__ == '__main__':
    test_url_routing()
