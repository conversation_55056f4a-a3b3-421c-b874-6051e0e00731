#!/usr/bin/env python
"""
Debug script to check Django URL patterns
"""
import os
import sys
import django

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pc_hardware.settings.dev')
django.setup()

from django.urls import get_resolver
from django.conf import settings

def print_url_patterns(urlpatterns, prefix=''):
    """Recursively print all URL patterns"""
    for pattern in urlpatterns:
        if hasattr(pattern, 'url_patterns'):
            # This is an include() pattern
            print(f"{prefix}{pattern.pattern} -> INCLUDE")
            print_url_patterns(pattern.url_patterns, prefix + "  ")
        else:
            # This is a regular pattern
            print(f"{prefix}{pattern.pattern} -> {pattern.name}")

def debug_staff_urls():
    """Debug staff URL patterns specifically"""
    print("🔍 Debugging Django URL patterns...\n")
    
    # Get the root URL resolver
    resolver = get_resolver()
    
    print("📋 All URL patterns:")
    print_url_patterns(resolver.url_patterns)
    
    print("\n" + "="*50)
    print("🎯 Looking for staff-related patterns...")
    
    # Look specifically for staff patterns
    for pattern in resolver.url_patterns:
        if hasattr(pattern, 'pattern') and 'staff' in str(pattern.pattern):
            print(f"\n📍 Found staff pattern: {pattern.pattern}")
            if hasattr(pattern, 'url_patterns'):
                print("   Sub-patterns:")
                print_url_patterns(pattern.url_patterns, "     ")
    
    print("\n" + "="*50)
    print("🔍 Checking product-types specifically...")
    
    # Try to find product-types patterns
    try:
        from apps.staff.products.urls import router
        print(f"\n📋 Staff products router patterns:")
        for pattern in router.urls:
            if 'product-types' in str(pattern.pattern):
                print(f"   {pattern.pattern} -> {pattern.name}")
    except Exception as e:
        print(f"❌ Error accessing staff products router: {e}")
    
    print("\n" + "="*50)
    print("🎯 Testing specific URL resolution...")
    
    # Test specific URLs
    test_urls = [
        '/api/staff/products/product-types/',
        '/api/staff/products/product-types/1/',
        '/api/staff/products/product-types/1/brands/',
        '/api/staff/products/product-types/1/associate_brands/',
    ]
    
    for url in test_urls:
        try:
            resolved = resolver.resolve(url)
            print(f"✅ {url} -> {resolved.func.__name__} ({resolved.view_name})")
        except Exception as e:
            print(f"❌ {url} -> {e}")

if __name__ == '__main__':
    debug_staff_urls()
